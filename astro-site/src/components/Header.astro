---
const { lang = 'en' } = Astro.props;

// Navigation menu items
const menuItems = {
  en: [
    { name: 'Home', url: '/' },
    { name: 'About', url: '/about' },
    { name: 'Services', url: '/service' },
    { name: 'Team', url: '/team' },
    { name: 'Pricing', url: '/pricing' },
    { name: 'FAQ', url: '/faq' },
    { name: 'Contact', url: '/contact' },
  ],
  fr: [
    { name: 'Accueil', url: '/fr' },
    { name: 'À propos', url: '/fr/about' },
    { name: 'Services', url: '/fr/service' },
    { name: 'Équipe', url: '/fr/team' },
    { name: 'Tarifs', url: '/fr/pricing' },
    { name: 'FAQ', url: '/fr/faq' },
    { name: 'Contact', url: '/fr/contact' },
  ]
};

const currentMenu = menuItems[lang as keyof typeof menuItems] || menuItems.en;
const homeText = lang === 'fr' ? 'Accueil' : 'Home';
const contactText = lang === 'fr' ? 'Contactez-nous' : 'Contact Us';
---

<!-- Navigation -->
<div class="naviagtion fixed-top transition">
  <div class="container">
    <nav class="navbar navbar-expand-lg navbar-dark p-0 flex items-center justify-between py-4">
      <a class="navbar-brand p-0" href={lang === 'fr' ? '/fr' : '/'}>
        <img src="/images/ptbl-logo.png" alt="PTBL | Enabling Connectivity" class="h-10 w-auto">
      </a>
      
      <button 
        class="navbar-toggler border-0 lg:hidden" 
        type="button" 
        data-toggle="collapse" 
        data-target="#navigation" 
        aria-controls="navigation"
        aria-expanded="false" 
        aria-label="Toggle navigation"
        id="navbar-toggler"
      >
        <i class="fa fa-bars text-gray-900 text-xl"></i>
      </button>

      <div class="collapse navbar-collapse text-center hidden lg:flex" id="navigation">
        <ul class="navbar-nav mx-auto flex items-center space-x-8">
          <li class="nav-item">
            <a class="nav-link text-gray-900 hover:text-primary transition-colors" href={lang === 'fr' ? '/fr' : '/'}>{homeText}</a>
          </li>
          {currentMenu.slice(1).map((item) => (
            <li class="nav-item">
              <a class="nav-link text-gray-900 hover:text-primary transition-colors" href={item.url}>{item.name}</a>
            </li>
          ))}
        </ul>

        <!-- Language Switcher -->
        <div class="flex items-center space-x-4">
          <select 
            id="select-language" 
            class="bg-transparent border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary"
            onchange="location = this.value;"
          >
            <option value="/" {lang === 'en' ? 'selected' : ''}>En</option>
            <option value="/fr" {lang === 'fr' ? 'selected' : ''}>Fr</option>
          </select>

          <!-- Contact Button -->
          <a href={lang === 'fr' ? '/fr/contact' : '/contact'} class="btn btn-outline-primary text-sm">
            {contactText}
          </a>
        </div>
      </div>

      <!-- Mobile Menu -->
      <div class="mobile-menu fixed inset-0 bg-white z-40 transform translate-x-full transition-transform duration-300 lg:hidden" id="mobile-menu">
        <div class="flex flex-col h-full">
          <div class="flex items-center justify-between p-4 border-b">
            <img src="/images/ptbl-logo.png" alt="PTBL" class="h-8 w-auto">
            <button id="close-mobile-menu" class="text-gray-500 hover:text-gray-700">
              <i class="fa fa-times text-xl"></i>
            </button>
          </div>
          
          <nav class="flex-1 px-4 py-6">
            <ul class="space-y-4">
              {currentMenu.map((item) => (
                <li>
                  <a href={item.url} class="block py-2 text-lg text-gray-900 hover:text-primary transition-colors">
                    {item.name}
                  </a>
                </li>
              ))}
            </ul>
            
            <div class="mt-8 pt-8 border-t">
              <select 
                class="w-full bg-transparent border border-gray-300 rounded px-3 py-2 mb-4 focus:outline-none focus:ring-2 focus:ring-primary"
                onchange="location = this.value;"
              >
                <option value="/" {lang === 'en' ? 'selected' : ''}>English</option>
                <option value="/fr" {lang === 'fr' ? 'selected' : ''}>Français</option>
              </select>
              
              <a href={lang === 'fr' ? '/fr/contact' : '/contact'} class="btn btn-primary w-full">
                {contactText}
              </a>
            </div>
          </nav>
        </div>
      </div>
    </nav>
  </div>
</div>

<script>
  // Mobile menu toggle
  document.addEventListener('DOMContentLoaded', function() {
    const toggler = document.getElementById('navbar-toggler');
    const mobileMenu = document.getElementById('mobile-menu');
    const closeBtn = document.getElementById('close-mobile-menu');
    
    if (toggler && mobileMenu) {
      toggler.addEventListener('click', function() {
        mobileMenu.classList.remove('translate-x-full');
      });
    }
    
    if (closeBtn && mobileMenu) {
      closeBtn.addEventListener('click', function() {
        mobileMenu.classList.add('translate-x-full');
      });
    }
    
    // Close mobile menu when clicking outside
    if (mobileMenu) {
      mobileMenu.addEventListener('click', function(e) {
        if (e.target === mobileMenu) {
          mobileMenu.classList.add('translate-x-full');
        }
      });
    }
  });
</script>

<style>
  .navbar-toggler:focus {
    outline: none;
    box-shadow: none;
  }
  
  .mobile-menu {
    backdrop-filter: blur(10px);
  }
  
  @media (max-width: 1023px) {
    .navbar-collapse {
      display: none !important;
    }
  }
</style>
