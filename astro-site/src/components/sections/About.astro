---
export interface Props {
  enable: boolean;
  about_item: Array<{
    image: string;
    subtitle?: string;
    title: string;
    content: string;
    button: {
      enable: boolean;
      label: string;
      link: string;
    };
  }>;
}

const { enable, about_item } = Astro.props;
---

{enable && (
  <section class="section">
    <div class="container">
      {about_item.map((item, index) => (
        <div class={`flex flex-col lg:flex-row items-center gap-12 ${index > 0 ? 'mt-24' : ''}`}>
          <div class={`lg:w-1/2 ${index % 2 === 1 ? 'lg:order-2' : ''}`}>
            <img 
              src={item.image} 
              alt={item.title} 
              class="w-full h-auto rounded-lg shadow-lg"
              data-aos={index % 2 === 0 ? "fade-right" : "fade-left"}
            >
          </div>
          <div class={`lg:w-1/2 ${index % 2 === 1 ? 'lg:order-1' : ''}`}>
            {item.subtitle && (
              <p class="subtitle" data-aos="fade-up">{item.subtitle}</p>
            )}
            <h2 class="section-title mb-6" data-aos="fade-up" data-aos-delay="200">{item.title}</h2>
            <p class="text-color text-lg leading-relaxed mb-8" data-aos="fade-up" data-aos-delay="400">
              {item.content}
            </p>
            {item.button.enable && (
              <a 
                href={item.button.link} 
                class="btn btn-primary"
                data-aos="fade-up" 
                data-aos-delay="600"
              >
                {item.button.label}
              </a>
            )}
          </div>
        </div>
      ))}
    </div>
  </section>
)}
