---
export interface Props {
  enable: boolean;
  subtitle?: string;
  title?: string;
  team_member: Array<{
    name: string;
    image: string;
    designation: string;
    bio?: string;
    social?: Array<{
      icon: string;
      link: string;
    }>;
  }>;
}

const { enable, subtitle, title, team_member } = Astro.props;
---

{enable && (
  <section class="section">
    <div class="container">
      {(subtitle || title) && (
        <div class="text-center mb-16">
          {subtitle && <p class="subtitle" data-aos="fade-up">{subtitle}</p>}
          {title && <h2 class="section-title" data-aos="fade-up" data-aos-delay="200">{title}</h2>}
        </div>
      )}
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {team_member.map((member, index) => (
          <div class="group relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay={200 * (index + 1)}>
            <div class="aspect-w-4 aspect-h-5 relative">
              <img 
                src={member.image} 
                alt={member.name} 
                class="w-full h-80 object-cover transition-transform duration-300 group-hover:scale-110"
              >
              
              <!-- Overlay content -->
              <div class="absolute inset-0 bg-gradient-to-t from-primary via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300">
                <div class="absolute bottom-0 left-0 right-0 p-6 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                  <h4 class="text-xl font-semibold mb-2">{member.name}</h4>
                  <p class="text-blue-200 mb-4">{member.designation}</p>
                  
                  {member.social && member.social.length > 0 && (
                    <div class="flex space-x-3">
                      {member.social.map((social) => (
                        <a 
                          href={social.link} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-300"
                        >
                          <i class={`${social.icon} text-white`}></i>
                        </a>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            <!-- Default content (visible when not hovering) -->
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/50 to-transparent p-6 text-white group-hover:opacity-0 transition-opacity duration-300">
              <h4 class="text-xl font-semibold mb-1">{member.name}</h4>
              <p class="text-gray-300">{member.designation}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  </section>
)}
