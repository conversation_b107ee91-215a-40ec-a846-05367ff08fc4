---
export interface Props {
  enable: boolean;
  items: Array<{
    title: string;
    content: string;
  }>;
}

const { enable, items } = Astro.props;
---

{enable && (
  <section class="section bg-gray-50">
    <div class="container">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {items.map((item, index) => (
          <div class="card p-8" data-aos="fade-up" data-aos-delay={200 * (index + 1)}>
            <h3 class="text-2xl font-bold mb-6 text-primary">{item.title}</h3>
            <p class="text-color text-lg leading-relaxed">{item.content}</p>
          </div>
        ))}
      </div>
    </div>
  </section>
)}
